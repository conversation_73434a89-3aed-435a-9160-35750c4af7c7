%-----------EXPERIENCE-----------%
\section{PROFESSIONAL EXPERIENCE}
\resumeSubHeadingListStart

\resumeSubheading
{Med4 Solutions}{January 2024 - July 2024}
{Software Engineering Intern}{Sfax, Tunisia}
\resumeItemListStart
    \resumeItem{Contributed to the development of a healthcare platform with two main components: a \textbf{Flutter mobile application for patients} and an \textbf{Angular web platform for pharmacists}.}
    \resumeItem{Implemented backend services using \textbf{NestJS} and \textbf{MongoDB}, including authentication, data management, and secure APIs.}
    \resumeItem{Integrated an \textbf{OCR module with AI techniques} to automate the extraction and validation of information from medical prescriptions.}
    \resumeItem{Collaborated on full-stack development, deployment, and testing to ensure scalability and reliability of the platform.}
\resumeItemListEnd

    
    \resumeSubheading
    {Digital Click}{June 2024 - August 2024}
    {Web Development $|$ Employee Time Tracking System}{Sfax, Tunisia}
    \resumeItemListStart
        \resumeItem{Development of a presence management application.}
        \resumeItem{Use of ASP.NET Core with Entity Framework and Identity for tracking employee entries and exits.}
        \resumeItem{Generation of monthly statistics.}
        \resumeItem{Display of summary charts.}
        \resumeItem{Integration of DataTables for column filtering and sorting.}
    \resumeItemListEnd
    
    \resumeSubheading
    {Zetta Box}{June 2023 - August 2023}
    {Web Development}{Sfax, Tunisia}
    \resumeItemListStart
        \resumeItem{Development of a web application for invoice management.}
        \resumeItem{Use of React JS for the front-end, enabling the creation of reactive user interfaces.}
        \resumeItem{Use of Strapi for the back-end, facilitating content management via a RESTful API.}
        \resumeItem{Strengthened web development skills, with a particular focus on user interfaces and APIs.}
    \resumeItemListEnd

\resumeSubHeadingListEnd
